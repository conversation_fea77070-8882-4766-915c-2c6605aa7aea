<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\BelongsToHotel;

class Payment extends Model
{
    use HasFactory, BelongsToHotel;

    protected $table = 'payments';
    protected $fillable = ['hotel_id', 'invoice_id', 'amount', 'payment_method', 'payment_date', 'created_by', 'is_remitted', 'current_location', 'provider_payment_id', 'provider_response'];

    protected $casts = [
        'payment_date' => 'datetime',
        'provider_response' => 'array'
    ];

    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function remittances()
    {
        return $this->hasMany(Remittance::class);
    }

    public function refunds()
    {
        return $this->hasMany(Refund::class);
    }

    // Helper methods for refunds
    public function canBeRefunded()
    {
        // Check if payment has any completed refunds
        $totalRefunded = $this->refunds()->where('status', 'completed')->sum('refund_amount');
        return $this->amount > $totalRefunded;
    }

    public function getRemainingRefundableAmountAttribute()
    {
        $totalRefunded = $this->refunds()->where('status', 'completed')->sum('refund_amount');
        return $this->amount - $totalRefunded;
    }

    public function hasRefunds()
    {
        return $this->refunds()->exists();
    }

    public function getRefundStatusAttribute()
    {
        $totalRefunded = $this->refunds()->where('status', 'completed')->sum('refund_amount');

        if ($totalRefunded == 0) {
            return 'none';
        } elseif ($totalRefunded >= $this->amount) {
            return 'full';
        } else {
            return 'partial';
        }
    }

    /**
     * Get PayMongo payment ID for refunds
     */
    public function getPayMongoPaymentId()
    {
        if ($this->payment_method === 'paymongo' && $this->provider_payment_id) {
            return $this->provider_payment_id;
        }
        return null;
    }

    /**
     * Get PayPal capture ID for refunds
     */
    public function getPayPalCaptureId()
    {
        if ($this->payment_method === 'paypal' && $this->provider_payment_id) {
            return $this->provider_payment_id;
        }
        return null;
    }
}
