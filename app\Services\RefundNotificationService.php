<?php

namespace App\Services;

use App\Models\Refund;
use App\Models\RefundPolicy;
use App\Mail\RefundNotificationMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class RefundNotificationService
{
    /**
     * Send refund status notification
     */
    public function sendRefundStatusNotification(Refund $refund, string $status, array $additionalData = [])
    {
        try {
            $policy = RefundPolicy::where('hotel_id', $refund->hotel_id)->first();
            
            if (!$policy) {
                return;
            }

            // Prepare notification data
            $data = [
                'refund' => $refund,
                'status' => $status,
                'hotel' => $refund->hotel,
                'guest_name' => $this->getGuestName($refund),
                'guest_email' => $this->getGuestEmail($refund),
                'additional_data' => $additionalData
            ];

            // Send guest notification
            if ($policy->notify_guest && $data['guest_email']) {
                $this->sendGuestNotification($data);
            }

            // Send admin notification
            if ($policy->notify_admin) {
                $this->sendAdminNotification($data);
            }

            // Send additional notification email
            if ($policy->notification_email) {
                $this->sendAdditionalNotification($data, $policy->notification_email);
            }

        } catch (\Exception $e) {
            Log::error('Failed to send refund notification', [
                'refund_id' => $refund->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send notification when refund is requested
     */
    public function sendRefundRequestedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'requested');
    }

    /**
     * Send notification when refund is approved
     */
    public function sendRefundApprovedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'approved');
    }

    /**
     * Send notification when refund is rejected
     */
    public function sendRefundRejectedNotification(Refund $refund, string $reason)
    {
        $this->sendRefundStatusNotification($refund, 'rejected', ['rejection_reason' => $reason]);
    }

    /**
     * Send notification when refund is processed
     */
    public function sendRefundProcessedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'processed');
    }

    /**
     * Send notification when refund is completed
     */
    public function sendRefundCompletedNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'completed');
    }

    /**
     * Send notification when refund fails
     */
    public function sendRefundFailedNotification(Refund $refund, string $error)
    {
        $this->sendRefundStatusNotification($refund, 'failed', ['error_message' => $error]);
    }

    /**
     * Send notification when refund is cancelled
     */
    public function sendRefundCancelledNotification(Refund $refund)
    {
        $this->sendRefundStatusNotification($refund, 'cancelled');
    }

    /**
     * Send guest notification
     */
    private function sendGuestNotification(array $data)
    {
        if (!$data['guest_email']) {
            return;
        }

        try {
            Mail::to($data['guest_email'])->send(
                new RefundNotificationMail(
                    $data['refund'],
                    $data['status'],
                    $data['additional_data'],
                    true // isGuestEmail
                )
            );

            Log::info('Guest refund notification sent', [
                'refund_id' => $data['refund']->id,
                'email' => $data['guest_email'],
                'status' => $data['status']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send guest refund notification', [
                'refund_id' => $data['refund']->id,
                'email' => $data['guest_email'],
                'status' => $data['status'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send admin notification
     */
    private function sendAdminNotification(array $data)
    {
        // Get hotel admin emails
        $adminEmails = $this->getHotelAdminEmails($data['refund']->hotel_id);

        if (empty($adminEmails)) {
            return;
        }

        foreach ($adminEmails as $email) {
            try {
                Mail::to($email)->send(
                    new RefundNotificationMail(
                        $data['refund'],
                        $data['status'],
                        $data['additional_data'],
                        false // isGuestEmail
                    )
                );

                Log::info('Admin refund notification sent', [
                    'refund_id' => $data['refund']->id,
                    'email' => $email,
                    'status' => $data['status']
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to send admin refund notification', [
                    'refund_id' => $data['refund']->id,
                    'email' => $email,
                    'status' => $data['status'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Send additional notification
     */
    private function sendAdditionalNotification(array $data, string $email)
    {
        try {
            Mail::to($email)->send(
                new RefundNotificationMail(
                    $data['refund'],
                    $data['status'],
                    $data['additional_data'],
                    false // isGuestEmail (treat as admin)
                )
            );

            Log::info('Additional refund notification sent', [
                'refund_id' => $data['refund']->id,
                'email' => $email,
                'status' => $data['status']
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send additional refund notification', [
                'refund_id' => $data['refund']->id,
                'email' => $email,
                'status' => $data['status'],
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get guest name from refund
     */
    private function getGuestName(Refund $refund): string
    {
        if ($refund->invoice && $refund->invoice->booking) {
            $booking = $refund->invoice->booking;
            return trim($booking->guest_first_name . ' ' . $booking->guest_last_name);
        }
        
        return 'Guest';
    }

    /**
     * Get guest email from refund
     */
    private function getGuestEmail(Refund $refund): ?string
    {
        if ($refund->invoice && $refund->invoice->booking) {
            return $refund->invoice->booking->guest_email;
        }
        
        return null;
    }

    /**
     * Get hotel admin emails
     */
    private function getHotelAdminEmails(int $hotelId): array
    {
        try {
            // Get users with admin roles for this hotel
            // This assumes you have a User model with hotel_id and role/permission system
            $adminEmails = \App\Models\User::where('hotel_id', $hotelId)
                ->where(function($query) {
                    $query->where('role', 'admin')
                          ->orWhere('role', 'manager')
                          ->orWhere('role', 'super_admin');
                })
                ->whereNotNull('email')
                ->pluck('email')
                ->toArray();

            return $adminEmails;
        } catch (\Exception $e) {
            Log::error('Failed to get hotel admin emails', [
                'hotel_id' => $hotelId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
