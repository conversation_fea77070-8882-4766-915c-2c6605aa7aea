<?php

use Illuminate\Database\Migrations\Migration;
use App\Models\Role;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add payments.refund permission to appropriate roles
        $rolesToUpdate = [
            'super-admin' => ['payments.refund'], // Super admin already has * wildcard, but let's be explicit
            'hotel-admin' => ['payments.refund'], // Hotel admin should have refund permissions
            'manager' => ['payments.refund'], // Manager should have refund permissions
            'front-desk' => ['payments.refund'], // Front desk should have refund permissions for customer service
        ];

        foreach ($rolesToUpdate as $roleSlug => $newPermissions) {
            $role = Role::where('slug', $roleSlug)->first();

            if ($role) {
                $currentPermissions = $role->permissions ?? [];

                // Add new permissions if they don't already exist
                foreach ($newPermissions as $permission) {
                    if (!in_array($permission, $currentPermissions)) {
                        $currentPermissions[] = $permission;
                    }
                }

                $role->update(['permissions' => $currentPermissions]);

                echo "Updated role '{$role->name}' with refund permissions.\n";
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove payments.refund permission from roles
        $rolesToUpdate = ['super-admin', 'hotel-admin', 'manager', 'front-desk'];

        foreach ($rolesToUpdate as $roleSlug) {
            $role = Role::where('slug', $roleSlug)->first();

            if ($role) {
                $currentPermissions = $role->permissions ?? [];

                // Remove payments.refund permission
                $currentPermissions = array_filter($currentPermissions, function($permission) {
                    return $permission !== 'payments.refund';
                });

                $role->update(['permissions' => array_values($currentPermissions)]);
            }
        }
    }
};
