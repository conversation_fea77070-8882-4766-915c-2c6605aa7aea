<?php

namespace App\Mail;

use App\Models\Refund;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class RefundNotificationMail extends Mailable
{
    use Queueable, SerializesModels;

    public $refund;
    public $status;
    public $additionalData;
    public $isGuestEmail;

    /**
     * Create a new message instance.
     */
    public function __construct(Refund $refund, string $status, array $additionalData = [], bool $isGuestEmail = true)
    {
        $this->refund = $refund;
        $this->status = $status;
        $this->additionalData = $additionalData;
        $this->isGuestEmail = $isGuestEmail;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = $this->getEmailSubject();

        return new Envelope(
            subject: $subject,
            from: config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            text: 'emails.refund-notification-text',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }

    /**
     * Get email subject based on status and recipient type
     */
    private function getEmailSubject(): string
    {
        $hotelName = $this->refund->hotel->name ?? 'Hotel';

        if ($this->isGuestEmail) {
            switch ($this->status) {
                case 'requested':
                    return "Refund Request Received - {$this->refund->refund_number}";
                case 'approved':
                    return "Refund Approved - {$this->refund->refund_number}";
                case 'rejected':
                    return "Refund Request Declined - {$this->refund->refund_number}";
                case 'processed':
                    return "Refund Being Processed - {$this->refund->refund_number}";
                case 'completed':
                    return "Refund Completed - {$this->refund->refund_number}";
                case 'failed':
                    return "Refund Processing Failed - {$this->refund->refund_number}";
                case 'cancelled':
                    return "Refund Cancelled - {$this->refund->refund_number}";
                default:
                    return "Refund Update - {$this->refund->refund_number}";
            }
        } else {
            // Admin email subjects
            switch ($this->status) {
                case 'requested':
                    return "New Refund Request - {$this->refund->refund_number}";
                case 'approved':
                    return "Refund Approved - {$this->refund->refund_number}";
                case 'rejected':
                    return "Refund Rejected - {$this->refund->refund_number}";
                case 'processed':
                    return "Refund Processed - {$this->refund->refund_number}";
                case 'completed':
                    return "Refund Completed - {$this->refund->refund_number}";
                case 'failed':
                    return "Refund Failed - {$this->refund->refund_number}";
                case 'cancelled':
                    return "Refund Cancelled - {$this->refund->refund_number}";
                default:
                    return "Refund Update - {$this->refund->refund_number}";
            }
        }
    }
}
