@php
    $currencySymbol = config('currency.symbol', '$');
    $hotelName = $refund->hotel->name ?? 'Hotel';
    $guestName = 'Guest';
    if ($refund->invoice && $refund->invoice->booking) {
        $booking = $refund->invoice->booking;
        $guestName = trim($booking->guest_first_name . ' ' . $booking->guest_last_name);
    }
@endphp

@if($isGuestEmail)
Dear {{ $guestName }},

@switch($status)
    @case('requested')
We have received your refund request for {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}.

Refund Number: {{ $refund->refund_number }}
Reason: {{ $refund->reason }}

We will review your request and notify you of the outcome.
        @break

    @case('approved')
Your refund request has been approved!

Refund Number: {{ $refund->refund_number }}
Amount: {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}

Your refund will be processed shortly.
        @break

    @case('rejected')
Unfortunately, your refund request has been declined.

Refund Number: {{ $refund->refund_number }}
@if(isset($additionalData['rejection_reason']))
Reason: {{ $additionalData['rejection_reason'] }}
@endif

If you have any questions, please contact us.
        @break

    @case('processed')
Your refund is being processed.

Refund Number: {{ $refund->refund_number }}
Amount: {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}

You should receive the refund in your original payment method within 3-5 business days.
        @break

    @case('completed')
Your refund has been completed!

Refund Number: {{ $refund->refund_number }}
Amount: {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}

The refund has been sent to your original payment method.
        @break

    @case('failed')
There was an issue processing your refund.

Refund Number: {{ $refund->refund_number }}

We are working to resolve this issue. Please contact us for assistance.
        @break

    @case('cancelled')
Your refund request has been cancelled.

Refund Number: {{ $refund->refund_number }}
        @break

    @default
Your refund status has been updated.

Refund Number: {{ $refund->refund_number }}
Status: {{ ucfirst($status) }}
@endswitch

Thank you for choosing {{ $hotelName }}.

Best regards,
{{ $hotelName }} Team

@else
{{-- Admin Email --}}
Refund Status Update

Refund Number: {{ $refund->refund_number }}
Guest: {{ $guestName }}
Amount: {{ $currencySymbol }}{{ number_format($refund->refund_amount, 2) }}
Payment Method: {{ ucfirst($refund->payment_method) }}
Status: {{ ucfirst($status) }}
Reason: {{ $refund->reason }}

@if(isset($additionalData['rejection_reason']))
Rejection Reason: {{ $additionalData['rejection_reason'] }}

@endif
@if(isset($additionalData['error_message']))
Error: {{ $additionalData['error_message'] }}

@endif
View refund details: {{ route('admin.refunds.show', $refund) }}

@endif

---
This is an automated message from {{ $hotelName }}.
@if($isGuestEmail)
If you have any questions, please contact us directly.
@endif
